<?php

namespace plugin\admin\app\controller;

use support\Request;
use support\Response;
use plugin\admin\app\model\Region;
use plugin\admin\app\controller\Crud;
use plugin\admin\app\common\Tree;
use support\exception\BusinessException;

/**
 * 地区数据管理
 */
class RegionController extends Crud
{

    /**
     * @var Region
     */
    protected $model = null;

    /**
     * 构造函数
     * @return void
     */
    public function __construct()
    {
        $this->model = new Region;
    }

    /**
     * 浏览
     * @return Response
     */
    public function index(): Response
    {
        return view('region/index');
    }

    /**
     * 查询 - 重写以支持树表格格式
     * @param Request $request
     * @return Response
     * @throws BusinessException
     */
    public function select(Request $request): Response
    {
        [$where, $format, $limit, $field, $order] = $this->selectInput($request);

        // 对于树表格，我们需要获取所有数据而不是分页
        if ($format === 'table_tree') {
            $limit = 10000; // 设置一个较大的限制以获取所有数据
        }

        $query = $this->doSelect($where, $field, $order);

        // 如果是树表格格式，使用自定义的格式化方法
        if ($format === 'table_tree') {
            return $this->formatRegionTableTree($query, $limit);
        }

        return $this->doFormat($query, $format, $limit);
    }

    /**
     * 格式化地区树表格数据
     * @param $query
     * @param $limit
     * @return Response
     */
    protected function formatRegionTableTree($query, $limit): Response
    {
        $items = $query->limit($limit)->get()->toArray();

        // 使用Tree类构建树形结构，使用'parent_id'作为父级字段
        $tree = new Tree($items, 'parent_id');
        $tree_data = $tree->getTree();

        return $this->json(0, 'ok', ['count' => count($items), 'data' => $tree_data]);
    }

    /**
     * 插入
     * @param Request $request
     * @return Response
     * @throws BusinessException
     */
    public function insert(Request $request): Response
    {
        if ($request->method() === 'POST') {
            return parent::insert($request);
        }
        return view('region/insert');
    }

    /**
     * 更新
     * @param Request $request
     * @return Response
     * @throws BusinessException
    */
    public function update(Request $request): Response
    {
        if ($request->method() === 'POST') {
            return parent::update($request);
        }
        return view('region/update');
    }

}
