
<!DOCTYPE html>
<html lang="zh-cn">
    <head>
        <meta charset="utf-8">
        <title>浏览页面</title>
        <link rel="stylesheet" href="/app/admin/component/pear/css/pear.css" />
        <link rel="stylesheet" href="/app/admin/admin/css/reset.css" />
    </head>
    <body class="pear-container">
    
        <!-- 顶部查询表单 -->
        
        
        <!-- 搜索表单 -->
        <div class="layui-card">
            <div class="layui-card-body">
                <form class="layui-form" action="">
                    <div class="layui-form-item">
                        <label class="layui-form-label">关键字</label>
                        <div class="layui-input-inline">
                            <input type="text" name="keyword" placeholder="搜索地区名称" class="layui-input">
                        </div>
                        <button class="pear-btn pear-btn-md pear-btn-primary" lay-submit lay-filter="region-query">
                            <i class="layui-icon layui-icon-search"></i>
                            查询
                        </button>
                        <button type="reset" class="pear-btn pear-btn-md">
                            <i class="layui-icon layui-icon-refresh"></i>
                            重置
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 数据表格 -->
        <div class="layui-card">
            <div class="layui-card-body">
                <table id="region-table" lay-filter="region-table"></table>
            </div>
        </div>

        <!-- 表格顶部工具栏 -->
        <script type="text/html" id="region-toolbar">
            <button class="pear-btn pear-btn-primary pear-btn-md" lay-event="add" permission="app.admin.region.insert">
                <i class="layui-icon layui-icon-add-1"></i>新增
            </button>
            <button class="pear-btn pear-btn-danger pear-btn-md" lay-event="batchRemove" permission="app.admin.region.delete">
                <i class="layui-icon layui-icon-delete"></i>删除
            </button>
            <button class="pear-btn pear-btn-success pear-btn-md" lay-event="expandAll">
                <i class="layui-icon layui-icon-spread-left"></i>展开
            </button>
            <button class="pear-btn pear-btn-success pear-btn-md" lay-event="foldAll">
                <i class="layui-icon layui-icon-shrink-right"></i>折叠
            </button>
            <button class="pear-btn pear-btn-success pear-btn-md" lay-event="reload">
                <i class="layui-icon layui-icon-refresh"></i>重载
            </button>
        </script>

        <!-- 表格行工具栏 -->
        <script type="text/html" id="region-bar">
            <button class="pear-btn pear-btn-primary pear-btn-sm" lay-event="edit" permission="app.admin.region.update">
                <i class="layui-icon layui-icon-edit"></i>
            </button>
            <button class="pear-btn pear-btn-danger pear-btn-sm" lay-event="remove" permission="app.admin.region.delete">
                <i class="layui-icon layui-icon-delete"></i>
            </button>
        </script>

        <!-- 是否启用模板 -->
        <script type="text/html" id="is-enable-tpl">
            {{#if (d.is_enable == '1') { }}
            <span class="layui-badge layui-bg-green">启用</span>
            {{# }else{ }}
            <span class="layui-badge">禁用</span>
            {{# } }}
        </script>

        <!-- 是否有下级地区模板 -->
        <script type="text/html" id="has-children-tpl">
            {{#if (d.has_children == '1') { }}
            <span class="layui-badge layui-bg-blue">是</span>
            {{# }else{ }}
            <span class="layui-badge layui-bg-gray">否</span>
            {{# } }}
        </script>

        <script src="/app/admin/component/layui/layui.js?v=2.8.12"></script>
        <script src="/app/admin/component/pear/pear.js"></script>
        <script src="/app/admin/admin/js/permission.js"></script>
        <script src="/app/admin/admin/js/common.js"></script>

        <script>

            // 相关常量
            const PRIMARY_KEY = "id";
            const SELECT_API = "/app/admin/region/select?format=table_tree";
            const UPDATE_API = "/app/admin/region/update";
            const DELETE_API = "/app/admin/region/delete";
            const INSERT_URL = "/app/admin/region/insert";
            const UPDATE_URL = "/app/admin/region/update";

            // 表格渲染
            layui.use(["table", "form", "common", "popup", "util", "treetable"], function() {
                let table = layui.table;
                let form = layui.form;
                let $ = layui.$;
                let common = layui.common;
                let util = layui.util;
                let treetable = layui.treetable;
                
				// 表头参数
				let cols = [
					{
						type: "checkbox",
						align: "center"
					},{
						title: "地区名称",
						field: "name",
						minWidth: 200,
						align: "left"
					},{
						title: "地区层级",
						field: "level",
						width: 100,
						align: "center",
						templet: function(d) {
							const levels = {1: '国家', 2: '省/直辖市', 3: '市', 4: '区/县'};
							return levels[d.level] || d.level;
						}
					},{
						title: "行政区划代码",
						field: "region_key",
						width: 150,
						align: "center"
					},{
						title: "英文名称",
						field: "name_en",
						width: 150,
						align: "center"
					},{
						title: "俄文名称",
						field: "name_ru",
						width: 150,
						align: "center"
					},{
						title: "是否有下级",
						field: "has_children",
						width: 100,
						align: "center",
						templet: "#has-children-tpl"
					},{
						title: "是否启用",
						field: "is_enable",
						width: 100,
						align: "center",
						templet: "#is-enable-tpl"
					},{
						title: "排序",
						field: "sort_order",
						width: 80,
						align: "center"
					},{
						title: "操作",
						templet: "#region-bar",
						width: 120,
						align: "center"
					}
				];

				// 渲染树表格
				function render()
				{
				    treetable.render({
				        treeColIndex: 1,              // 树形列索引（地区名称列）
				        treeIdName: 'id',             // 树形id字段名
				        treePidName: 'parent_id',     // 树形父id字段名
				        skin: 'line',                 // 表格皮肤
				        treeDefaultClose: true,       // 默认折叠
				        toolbar: '#region-toolbar',   // 工具栏
				        elem: '#region-table',        // 表格元素
				        url: SELECT_API,              // 数据接口
				        page: false,                  // 不分页
				        cols: [cols]                  // 列配置
				    });
				}

				// 直接渲染表格
				render();
				
                // 编辑或删除行事件
                table.on("tool(region-table)", function(obj) {
                    if (obj.event === "remove") {
                        remove(obj);
                    } else if (obj.event === "edit") {
                        edit(obj);
                    }
                });

                // 表格顶部工具栏事件
                table.on("toolbar(region-table)", function(obj) {
                    if (obj.event === "add") {
                        add();
                    } else if (obj.event === "refresh") {
                        refreshTable();
                    } else if (obj.event === "batchRemove") {
                        batchRemove(obj);
                    } else if (obj.event === "expandAll") {
                        treetable.expandAll("#region-table");
                    } else if (obj.event === "foldAll") {
                        treetable.foldAll("#region-table");
                    } else if (obj.event === "reload") {
                        treetable.reload("#region-table");
                    }
                });

                // 树表格搜索事件
                form.on("submit(region-query)", function(data) {
                    var keyword = data.field.keyword;
                    treetable.search('#region-table', keyword);
                    return false;
                });
                
                // 字段允许为空
                form.verify({
                    phone: [/(^$)|^1\d{10}$/, "请输入正确的手机号"],
                    email: [/(^$)|^([a-zA-Z0-9_\.\-])+\@(([a-zA-Z0-9\-])+\.)+([a-zA-Z0-9]{2,4})+$/, "邮箱格式不正确"],
                    url: [/(^$)|(^#)|(^http(s*):\/\/[^\s]+\.[^\s]+)/, "链接格式不正确"],
                    number: [/(^$)|^\d+$/,'只能填写数字'],
                    date: [/(^$)|^(\d{4})[-\/](\d{1}|0\d{1}|1[0-2])([-\/](\d{1}|0\d{1}|[1-2][0-9]|3[0-1]))*$/, "日期格式不正确"],
                    identity: [/(^$)|(^\d{15}$)|(^\d{17}(x|X|\d)$)/, "请输入正确的身份证号"]
                });

                // 表格排序事件
                table.on("sort(data-table)", function(obj){
                    table.reload("data-table", {
                        initSort: obj,
                        scrollPos: "fixed",
                        where: {
                            field: obj.field,
                            order: obj.type
                        }
                    });
                });

                // 表格新增数据
                let add = function() {
                    layer.open({
                        type: 2,
                        title: "新增",
                        shade: 0.1,
                        maxmin: true,
                        area: [common.isModile()?"100%":"500px", common.isModile()?"100%":"450px"],
                        content: INSERT_URL
                    });
                }

                // 表格编辑数据
                let edit = function(obj) {
                    let value = obj.data[PRIMARY_KEY];
                    layer.open({
                        type: 2,
                        title: "修改",
                        shade: 0.1,
                        maxmin: true,
                        area: [common.isModile()?"100%":"500px", common.isModile()?"100%":"450px"],
                        content: UPDATE_URL + "?" + PRIMARY_KEY + "=" + value
                    });
                }

                // 删除一行
                let remove = function(obj) {
                    return doRemove(obj.data[PRIMARY_KEY]);
                }

                // 删除多行
                let batchRemove = function(obj) {
                    let checkIds = common.checkField(obj, PRIMARY_KEY);
                    if (checkIds === "") {
                        layui.popup.warning("未选中数据");
                        return false;
                    }
                    doRemove(checkIds.split(","));
                }

                // 执行删除
                let doRemove = function (ids) {
                    let data = {};
                    data[PRIMARY_KEY] = ids;
                    layer.confirm("确定删除?", {
                        icon: 3,
                        title: "提示"
                    }, function(index) {
                        layer.close(index);
                        let loading = layer.load();
                        $.ajax({
                            url: DELETE_API,
                            data: data,
                            dataType: "json",
                            type: "post",
                            success: function(res) {
                                layer.close(loading);
                                if (res.code) {
                                    return layui.popup.failure(res.msg);
                                }
                                return layui.popup.success("操作成功", refreshTable);
                            }
                        })
                    });
                }

                // 刷新表格数据
                window.refreshTable = function() {
                    treetable.reload("#region-table");
                }
            })

        </script>
    </body>
</html>
